import type { ProcessTaskDetailResponse } from '#/entity/process';
import useNLRequest from '@/hooks/use-nl-request';
import { Button, Tabs } from 'antd-mobile';
import type React from 'react';
import { useMemo, useState } from 'react';
import { useParams } from 'react-router';
import TaskRecord from './components/task-record';
import TaskPreview from './components/task-preview';
import Timeline from './components/timeline';
import TaskActionBar from './components/task-action-bar';
import NLFormLayout, { useNLForm } from '@/components/nl-form/nl-form-layout';
import NLFormCore from '@/components/nl-form/nl-form-core';
import useActions, { type AllAction } from './hooks/use-actions';
import useActionExecutor from './hooks/use-action-executor';

const TaskDetailPage: React.FC = () => {
  const {processTaskId} = useParams<{processTaskId: string}>();
  const nlFormRef = useNLForm();

  // 获取可处理的步骤列表
  const {data: nextStepList } = useNLRequest<{
    status: string;
    tbodyList: Array<{
      id: string;
      name: string;
      [key: string]: any;
    }>;
  }>(
    processTaskId ? ['/api/rest/processtask/processablestep/list', {
      processTaskId
    }] : null,
  );

  const currentProcessTaskStepId = useMemo(() => {
    const tbodyList = nextStepList?.tbodyList || [];
      // 仿照PC端逻辑：如果只有一个可处理步骤，则自动设置
      if (tbodyList.length === 1) {
        return tbodyList[0].id;
      }
  }, [nextStepList])
    // 获取工单基本信息（包含表单数据）
  const { data: processTaskDetailResponse } = useNLRequest<ProcessTaskDetailResponse>(processTaskId && currentProcessTaskStepId ? ['/api/rest/processtask/step/get', {processTaskId, processTaskStepId: currentProcessTaskStepId}]: null);

  const { actionObj, moreActions, primaryActions} = useActions({
    processTaskId,
    processTaskStepId: currentProcessTaskStepId
  })

  const [activeTab, setActiveTab] = useState('detail');

  const renderContent = useMemo(() => {
    const isReadolny = !!actionObj.save;
    if (processTaskDetailResponse?.processTask.formConfig._type === 'new') {
      return (
        <NLFormLayout isEditable={isReadolny} ref={nlFormRef} draft={processTaskDetailResponse.processTask} processInfo={{
          channelPath: '',
          color: '',
          icon: '',
          description: '',
          channelUuid: '',
          priorityUuid: '',
        }}>
          <NLFormCore />
        </NLFormLayout>
      )
    }
    return <TaskPreview processTaskConfig={processTaskDetailResponse?.processTask} />
  }, [processTaskDetailResponse, nlFormRef, actionObj])

  const executors = useActionExecutor({processTaskId, processTaskStepId: currentProcessTaskStepId})
  const handleExecuteAction = (actionKey: AllAction, actionText: string) => {
    executors[actionKey]?.(actionText);
  };


  return (
    <div className='h-full flex flex-col'>
      <div className='flex-1 overflow-hidden'>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <Tabs.Tab title='内容详情' key='detail'>
            {renderContent}
          </Tabs.Tab>
          <Tabs.Tab title='步骤日志' key='record'>
            <TaskRecord processTaskId={processTaskId}/>
          </Tabs.Tab>
          <Tabs.Tab title='时间线' key='timeline'>
            <Timeline processTaskId={processTaskId}/>
          </Tabs.Tab>
        </Tabs>
      </div>
      <Button onClick={() => nlFormRef.current?.form.submit()}>text</Button>

      {/* 操作栏 - 固定在底部 */}
      {processTaskId && (
        <TaskActionBar
          moreActions={moreActions}
          primaryActions={primaryActions}
          onExecuteAction={handleExecuteAction}
        />
      )}
    </div>
  )
}

export default TaskDetailPage;
