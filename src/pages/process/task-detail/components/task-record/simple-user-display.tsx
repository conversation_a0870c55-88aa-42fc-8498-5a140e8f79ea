import type React from 'react';

interface SimpleUserDisplayProps {
  userName?: string;
  userUuid?: string;
  showAvatar?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const SimpleUserDisplay: React.FC<SimpleUserDisplayProps> = ({
  userName,
  showAvatar = true,
  size = 'small',
  className = ''
}) => {
  const sizeClasses = {
    small: 'w-6 h-6 text-xs',
    medium: 'w-8 h-8 text-sm',
    large: 'w-10 h-10 text-base'
  };


  const displayName = userName || '未知用户';
  const userInitial = displayName.charAt(0).toUpperCase();

  // 根据用户名生成颜色
  const generateColor = (name: string) => {
    const colors = [
      'from-blue-500 to-blue-600',
      'from-green-500 to-green-600',
      'from-purple-500 to-purple-600',
      'from-pink-500 to-pink-600',
      'from-indigo-500 to-indigo-600',
      'from-yellow-500 to-yellow-600',
      'from-red-500 to-red-600',
      'from-teal-500 to-teal-600'
    ];
    
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  };

  const colorClass = generateColor(displayName);

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* 头像 */}
      {showAvatar && (
        <div className={`
          ${sizeClasses[size]}
          bg-gradient-to-br ${colorClass}
          rounded-full
          flex items-center justify-center
          text-white
          shadow-sm
          flex-shrink-0
        `}>
          <span className="font-semibold">
            {userInitial}
          </span>
        </div>
      )}

      {/* 用户名 */}
      <span className="text-sm font-medium text-gray-700 truncate">
        {displayName}
      </span>
    </div>
  );
};

export default SimpleUserDisplay;
