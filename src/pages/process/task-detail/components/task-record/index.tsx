import type { NLProcessRecord } from '#/entity/process';
import { Card, Collapse } from 'antd-mobile';
import type React from 'react';
import { useMemo, useState } from 'react';
import TaskRecordMajorInfo from './task-record-major-info';
import TaskRecordCommentInfo from './task-record-comment-info';
import './task-record.css';
import useNLRequest from '@/hooks/use-nl-request';

interface TaskRecordProps {
  expandAll?: boolean;
  processTaskId?: string;
}

const TaskRecord: React.FC<TaskRecordProps> = ({ processTaskId, expandAll = true }) => {
  const { data: recordList } = useNLRequest<NLProcessRecord[]>(['/api/rest/processtask/step/list', {processTaskId }]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  // 处理步骤数据，按时间倒序排列
  const sortedRecordList = useMemo(() => {
    if (!recordList || recordList.length === 0) {
      return [];
    }
    return [...recordList].sort((a, b) => (b.startTime || 0) - (a.startTime || 0));
  }, [recordList]);

  // 初始化展开状态
  useMemo(() => {
    if (expandAll && sortedRecordList.length > 0) {
      setExpandedKeys(sortedRecordList.map(item => String(item.id)));
    } else {
      // 默认只展开第一个（最新的）步骤
      setExpandedKeys(sortedRecordList.length > 0 ? [String(sortedRecordList[0].id)] : []);
    }
  }, [expandAll, sortedRecordList]);

  if (!sortedRecordList || sortedRecordList.length === 0) {
    return (
      <div className="p-4 text-center">
        <div className="py-12">
          <div className="text-6xl text-gray-300 mb-4">📋</div>
          <div className="text-base text-gray-500">暂无步骤记录</div>
        </div>
      </div>
    );
  }

  return (
    <div className="task-record-container p-4">
      {/* 展开/收起控制 */}
      <div className="mb-4 flex justify-end">
        <button
          type="button"
          className="text-sm text-blue-600 hover:text-blue-800"
          onClick={() => {
            if (expandedKeys.length === sortedRecordList.length) {
              setExpandedKeys([]);
            } else {
              setExpandedKeys(sortedRecordList.map(item => String(item.id)));
            }
          }}
        >
          {expandedKeys.length === sortedRecordList.length ? '收起全部' : '展开全部'}
        </button>
      </div>

      {/* 步骤时间线 */}
      <div className="task-record-timeline">
        {sortedRecordList.map((item, index) => (
          <div key={item.id} className="task-record-item relative">
            {/* 时间线连接线 */}
            {index < sortedRecordList.length - 1 && (
              <div className="task-record-line" />
            )}

            {/* 时间线节点 */}
            <div className="flex">
              <div className={`task-record-node ${item.statusVo?.status || 'default'}`}>
                <div className="task-record-node-inner" />
              </div>

              <div className="flex-1 pb-6">
                <Card className="task-record-card bg-white">
                  <Collapse
                    activeKey={expandedKeys}
                    onChange={(keys) => setExpandedKeys(keys as string[])}
                  >
                    <Collapse.Panel
                      key={String(item.id)}
                      title={<TaskRecordMajorInfo record={item} />}
                    >
                      <TaskRecordCommentInfo record={item} />
                    </Collapse.Panel>
                  </Collapse>
                </Card>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TaskRecord;
