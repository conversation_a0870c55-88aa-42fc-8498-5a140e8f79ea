import type React from 'react';
import { Button, ActionSheet } from 'antd-mobile';
import './task-action-bar.css';
import type { ActionBarItem, AllAction } from '../../hooks/use-actions';


interface TaskActionBarProps {
  moreActions: ActionBarItem[]
  primaryActions: ActionBarItem[];
  onExecuteAction?: (actionKey: AllAction, actionText: string) => void;
  onActionComplete?: () => void;
}


const TaskActionBar: React.FC<TaskActionBarProps> = ({
  moreActions,
  primaryActions,
  onExecuteAction
}) => {

  // 执行操作
  const executeAction = async (actionKey: AllAction, actionText: string) => {
    onExecuteAction?.(actionKey, actionText);
  };

  // 获取按钮样式
  const getButtonType = (actionValue: string) => {
    switch (actionValue) {
      case 'start':
      case 'accept':
        return 'primary';
      case 'complete':
        return 'success';
      case 'save':
        return 'default';
      default:
        return 'default';
    }
  };

  // 处理更多操作的点击
  const handleMoreActions = () => {
    if (moreActions.length === 0) return;

    const actions = moreActions.map(action => ({
      key: action.key,
      text: action.text,
      disabled: !action.supported,
      description: action.supported ? undefined : '暂不支持此操作'
    }));

    ActionSheet.show({
      style: {
        background: 'white'
      },
      actions,
      onAction: (action) => {
        if (!action.disabled) {
          const targetAction = moreActions.find(a => a.key === action.key);
          if (targetAction) {
            executeAction(targetAction.key, targetAction.text);
          }
        }
      }
    });
  };

  if (primaryActions.length === 0 && moreActions.length === 0) {
    return null;
  }

  return (
    <div className="task-action-bar">
      <div className="action-buttons">
        {/* 主要操作按钮 */}
        {primaryActions.map((action) => (
          <Button
            key={action.key}
            className={`action-button action-${action.key} ${!action.supported ? 'action-disabled' : ''}`}
            color={getButtonType(action.key) as any}
            disabled={!action.supported}
            onClick={() => {
              if (action.supported) {
                executeAction(action.key, action.text);
              }
            }}
            size="small"
          >
            <span className="action-text">{action.text}</span>
          </Button>
        ))}

        {/* 更多操作按钮 */}
        {moreActions.length > 0 && (
          <Button
            className="action-button action-more"
            color="default"
            onClick={handleMoreActions}
            size="small"
          >
            <span className="action-icon">⋯</span>
            <span className="action-text">更多</span>
          </Button>
        )}
      </div>
    </div>
  );
};

export default TaskActionBar;
