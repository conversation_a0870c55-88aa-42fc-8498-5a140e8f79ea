import type { ProcessTaskDraft } from '#/entity/process';
import type React from 'react';
import { Card } from 'antd-mobile';
import { useMemo } from 'react';

interface TaskPreviewProps {
  processTaskConfig?: ProcessTaskDraft;
}

// 渲染不同类型的表单值
const renderFormValue = (value: any, handler: string): React.ReactNode => {
  if (value === undefined || value === null || value === '') {
    return <span className="text-gray-400">无</span>;
  }

  // 根据表单组件类型进行不同的渲染
  switch (handler) {
    case 'formtext':
    case 'formtextarea':
    case 'formpassword':
      return (
        <div className="whitespace-pre-wrap break-words">
          {String(value)}
        </div>
      );

    case 'formdate':
    case 'formtime':
      // 处理时间戳
      if (typeof value === 'number') {
        return new Date(value).toLocaleString('zh-CN');
      }
      return String(value);

    case 'formselect':
    case 'formradio':
    case 'formcheckbox':
      // 处理选择类组件
      if (Array.isArray(value)) {
        return value.map((item, index) => (
          <span key={index} className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded mr-1 mb-1">
            {typeof item === 'object' ? item.text || item.value || JSON.stringify(item) : String(item)}
          </span>
        ));
      }
      if (typeof value === 'object') {
        return (
          <span className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded">
            {value.text || value.value || JSON.stringify(value)}
          </span>
        );
      }
      return String(value);

    case 'formnumber':
    case 'formrate':
      return <span className="font-mono">{String(value)}</span>;

    case 'formupload':
      // 处理文件上传
      if (Array.isArray(value)) {
        return (
          <div className="space-y-1">
            {value.map((file, index) => (
              <div key={index} className="flex items-center space-x-2 text-blue-600">
                <span>📎</span>
                <span>{file.name || file.fileName || '未知文件'}</span>
              </div>
            ))}
          </div>
        );
      }
      return String(value);

    case 'formuserselect':
      // 处理用户选择
      if (Array.isArray(value)) {
        return value.map((user, index) => (
          <span key={index} className="inline-block bg-green-100 text-green-800 px-2 py-1 rounded mr-1 mb-1">
            {user.name || user.userName || user.text || String(user)}
          </span>
        ));
      }
      if (typeof value === 'object') {
        return (
          <span className="inline-block bg-green-100 text-green-800 px-2 py-1 rounded">
            {value.name || value.userName || value.text || JSON.stringify(value)}
          </span>
        );
      }
      return String(value);

    default:
      // 默认处理
      if (typeof value === 'string') {
        return (
          <div className="whitespace-pre-wrap break-words">
            {value}
          </div>
        );
      }
      if (typeof value === 'object') {
        return (
          <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
            {JSON.stringify(value, null, 2)}
          </pre>
        );
      }
      return String(value);
  }
};

const TaskPreview: React.FC<TaskPreviewProps> = (props) => {
  const { processTaskConfig } = props;

  // 处理工单基本信息
  const basicInfo = useMemo(() => {
    if (!processTaskConfig) return null;
    
    return {
      title: processTaskConfig.title || '无标题',
      priority: processTaskConfig.priorityUuid,
      channelPath: processTaskConfig.channelPath,
      // 可以根据需要添加更多字段
    };
  }, [processTaskConfig]);

  // 处理表单数据
  const formData = useMemo(() => {
    if (!processTaskConfig?.formConfig || !processTaskConfig?.formAttributeDataMap) {
      return null;
    }

    const { formConfig, formAttributeDataMap } = processTaskConfig;
    const formItems: Array<{
      label: string;
      value: any;
      uuid: string;
      handler: string;
    }> = [];

    // 遍历表单配置，提取表单项
    if (formConfig.tableList) {
      formConfig.tableList.forEach(table => {
        if (table.component?.uuid) {
          const uuid = table.component.uuid;
          const value = formAttributeDataMap[uuid];

          // 只显示有值的字段，并且排除布局组件
          if (value !== undefined && value !== null && value !== '' &&
              table.component.hasValue &&
              !['formdivider', 'formtab', 'formcollapse'].includes(table.component.handler)) {
            formItems.push({
              label: table.component.label || '未知字段',
              value: value,
              uuid: uuid,
              handler: table.component.handler
            });
          }
        }
      });
    }

    return formItems;
  }, [processTaskConfig]);

  if (!processTaskConfig) {
    return (
      <div className="p-4 text-center">
        <div className="py-12">
          <div className="text-6xl text-gray-300 mb-4">📋</div>
          <div className="text-base text-gray-500">暂无内容详情</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      {/* 基本信息 */}
      {basicInfo && (
        <Card className="rounded-xl shadow-sm border-0 bg-white">
          <div className="space-y-3">
            <div className="text-lg font-semibold text-gray-900">
              {basicInfo.title}
            </div>
            {basicInfo.channelPath && (
              <div className="text-sm text-gray-500">
                分类：{basicInfo.channelPath}
              </div>
            )}
          </div>
        </Card>
      )}

      {/* 表单内容 */}
      {formData && formData.length > 0 && (
        <Card className="rounded-xl shadow-sm border-0 bg-white">
          <div className="space-y-4">
            <div className="text-base font-medium text-gray-900 border-b border-gray-100 pb-2">
              表单内容
            </div>
            {formData.map((item) => (
              <div key={item.uuid} className="space-y-1">
                <div className="text-sm font-medium text-gray-700">
                  {item.label}
                </div>
                <div className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">
                  {renderFormValue(item.value, item.handler)}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 如果没有表单数据 */}
      {(!formData || formData.length === 0) && (
        <Card className="rounded-xl shadow-sm border-0 bg-white">
          <div className="text-center py-8">
            <div className="text-4xl text-gray-300 mb-2">📝</div>
            <div className="text-sm text-gray-500">暂无表单内容</div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default TaskPreview;
