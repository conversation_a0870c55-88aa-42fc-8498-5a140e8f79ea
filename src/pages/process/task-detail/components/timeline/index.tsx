import type { NLProcessTaskDetail } from '#/entity/process';
import type React from 'react';
import { Card } from 'antd-mobile';
import { useMemo } from 'react';
import UserProfile from '@/components/user-profile';
import TimelineActivityItem from './timeline-activity-item';
import './timeline.css';
import useNLRequest from '@/hooks/use-nl-request';

interface TimelineProps {
  processTaskId?: string;
}

const Timeline: React.FC<TimelineProps> = ({processTaskId}) => {
  const { data: auditList } = useNLRequest<NLProcessTaskDetail[]>(['/api/rest/processtask/audit/list', {processTaskId }]);

  // 处理时间线数据
  const timelineData = useMemo(() => {
    if (!auditList || auditList.length === 0) {
      return [];
    }

    // 按时间倒序排列（最新的在前面）
    return auditList.sort((a, b) => b.actionTime - a.actionTime);
  }, [auditList]);

  if (!timelineData.length) {
    return (
      <div className="p-4 text-center">
        <div className="py-12">
          <div className="text-6xl text-gray-300 mb-4">⏰</div>
          <div className="text-base text-gray-500">暂无时间线数据</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="timeline-container">
        {timelineData.map((item, index) => (
          <div key={item.id} className="timeline-item relative">
            {/* 时间线连接线 */}
            {index < timelineData.length - 1 && (
              <div className="timeline-line" />
            )}

            {/* 时间线节点 */}
            <div className="flex">
              <div className="timeline-node">
                <div className="timeline-node-inner" />
              </div>

              <div className="flex-1 pb-8">
                <Card className="timeline-card bg-white">
                  {/* 头部信息 */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {/* 用户信息 */}
                      {item.userVo && item.userVo.uuid !== 'system' ? (
                        <UserProfile
                          showAvatar={false}
                          roleUuid={item.userVo.uuid}
                          roleName={item.userVo.name}
                          userVo={item.userVo}
                        />
                      ) : (
                        <span className="text-sm text-gray-600 font-medium">系统</span>
                      )}

                      {/* 操作描述 */}
                      <span
                        className="text-sm text-green-600"
                        dangerouslySetInnerHTML={{ __html: item.description }}
                      />

                    </div>

                    {/* 时间 */}
                    <div className="text-xs text-gray-400">
                      {new Date(item.actionTime).toLocaleString('zh-CN')}
                    </div>
                  </div>

                  {/* 活动详情列表 */}
                  {item.auditDetailList && item.auditDetailList.length > 0 && (
                    <div className="space-y-2">
                      {item.auditDetailList.map((auditDetail, detailIndex) => (
                        <TimelineActivityItem
                          key={`${item.id}-${detailIndex}`}
                          auditDetail={auditDetail}
                          formSceneUuid={item.formSceneUuid}
                          processTaskStepId={item.processTaskStepId}
                        />
                      ))}
                    </div>
                  )}
                </Card>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Timeline;
