import useNLRequest from "@/hooks/use-nl-request";
import { useMemo } from "react";
import { toast } from "sonner";

interface UseActionsOptions {
  processTaskId?: string;
  processTaskStepId?: string;
}

interface TaskAction {
  value: string;
  text: string;
}

export interface ActionBarItem {key: AllAction; text: string; supported: boolean}

export enum AllAction {
  START = 'start',
  ACCEPT = 'accept',
  UPDATE = 'update',
  ABORTPROCESSTASK = 'abortprocessTask',
  RECOVERPROCESSTASK = 'recoverprocessTask',
  COMPLETE = 'complete',
  RETREAT = 'retreat',
  SAVE = 'save',
  TRANSFER = 'transfer',
  COMMENT = 'comment',
  CREATESUBTASK = 'createsubtask',
  BACK = 'back',
  POCESSTASKVIEW = 'pocesstaskview',
  URGE = 'urge',
  PAUSE = 'pause',
  RECOVER = 'recover',
  TRANFERREPORT = 'tranferreport',
  COPYPROCESSTASK = 'copyprocesstask',
  REDO = 'redo',
  MARKREPEAT = 'markrepeat',
  REAPPROVAL = 'reapproval',
  CREATETASK = 'createtask',
  TRANSFEREOASTEP = 'transfereoastep',
  REACTIVATE = 'reactivate',
}

const actionConfig: Record<AllAction, string | null> = {
  //当前权限按钮展示
  start: null, //开始
  accept: null, // 开始
  update: null, //修改上报内容
  abortprocessTask: null, //终止||取消
  recoverprocessTask: null, //恢复
  complete: null, //完成||流转
  retreat: null, //撤回
  save: null, //暂存
  transfer: null, //转交
  comment: null, //回复
  createsubtask: null, //创建子任务
  back: null, //回退
  pocesstaskview: null, //查看工单权限
  urge: null, //催办
  pause: null, //暂停工单步骤
  recover: null, // 恢复工单步骤
  tranferreport: null, // 转报
  copyprocesstask: null, //复制上报
  redo: null, //评分之前回退
  markrepeat: null, // 标记重复事件
  reapproval: null, // 重审
  createtask: null, //创建任务（新的子任务）
  transfereoastep: null, // eoa转交
  reactivate: null //重新激活
}

const supportActions: Array<AllAction> = [AllAction.START, AllAction.ACCEPT, AllAction.ABORTPROCESSTASK, AllAction.RECOVERPROCESSTASK, AllAction.RETREAT, AllAction.SAVE, AllAction.TRANSFER, AllAction.PAUSE, AllAction.RECOVER,]

export default function useActions({processTaskId, processTaskStepId}: UseActionsOptions) {
    const { data: actionList, isLoading: loading } = useNLRequest<TaskAction[]>(
    (processTaskId && processTaskStepId) ? ['/api/rest/processtask/step/action/list', {
      processTaskId,
      processTaskStepId
    }] : null,
    {
      onError: (error) => {
        console.error('获取操作权限失败:', error);
        toast.error('获取操作权限失败');
      }
    }
  );


  // 仿照PC端处理actionConfig的逻辑
  const processedActionConfig = useMemo(() => {
    const config: Record<AllAction, string | null> = { ...actionConfig };
    // 遍历actionList，将有权限的操作设置为对应的文本
    if (actionList) {
      actionList.forEach(item => {
        for (const key in config) {
          if (key === item.value) {
            config[key as keyof typeof config] = item.text;
          }
        }
      });
    }

    return config;
  }, [actionList]);

  // 获取可用的操作列表（包含支持状态和按钮限制）
  const { primaryActions, moreActions } = useMemo(() => {
    const allActions: Array<ActionBarItem> = [];

    for (const key in processedActionConfig) {
      const text = processedActionConfig[key as AllAction];
      if (text) {
        allActions.push({
          key: key as AllAction,
          text,
          supported: supportActions.includes(key as AllAction)
        });
      }
    }

    // 按支持状态排序，支持的操作优先显示
    allActions.sort((a, b) => {
      if (a.supported && !b.supported) return -1;
      if (!a.supported && b.supported) return 1;
      return 0;
    });

    // 最多显示3个按钮，其余收起
    const maxPrimaryButtons = 3;
    const primary = allActions.slice(0, maxPrimaryButtons);
    const more = allActions.slice(maxPrimaryButtons);

    return {
      primaryActions: primary,
      moreActions: more
    };
  }, [processedActionConfig]);

  return {
    primaryActions,
    moreActions,
    loading,
    actionObj: processedActionConfig,
  }
}