
  
import { Dialog } from 'antd-mobile';
import { startTask, saveTaskDetail, completeTask } from '@/api/process';
import { toast } from 'sonner';
import { useCallback } from 'react';
import { AllAction } from './use-actions';


interface UseActionExecutorOptions {
  processTaskId?: string;
  processTaskStepId?: string;
}

const onActionComplete = () => {
  window.location.reload();
}

export default function useActionExecutor({processTaskId, processTaskStepId}: UseActionExecutorOptions): Partial<Record<AllAction, (actionText: string) => void>> {
   // 开始任务
  const handleStartTask = useCallback(async (actionText: string) => {
    if (!processTaskStepId || !processTaskId) {
      toast.error('缺少步骤ID');
      return;
    }

    const response = await startTask({
      processTaskId,
      processTaskStepId,
      action: 'accept'
    });

    if (response.data.data.Status === 'OK') {
      toast.success(`${actionText}成功`);
      onActionComplete?.();
    } else {
      throw new Error(response.data.message || '操作失败');
    }
  }, [processTaskId, processTaskStepId]);

  // 保存任务
  const handleSaveTask = useCallback(async (actionText: string) => {
    // 这里需要获取表单数据，暂时使用空对象
    const formData = {};

    const response = await saveTaskDetail({
      processTaskId,
      processTaskStepId,
      formData
    });

    if (response.data.data.Status === 'OK') {
      toast.success(`${actionText}成功`);
      onActionComplete?.();
    } else {
      throw new Error(response.data.message || '保存失败');
    }
  }, [processTaskId, processTaskStepId]);

  // 完成任务
  const handleCompleteTask = useCallback(async (actionText: string) => {
    Dialog.confirm({
      content: `确定要${actionText}吗？`,
      bodyStyle: {
        background: 'white'
      },
      onConfirm: async () => {
        const response = await completeTask({
          processTaskId,
          processTaskStepId,
          action: 'complete'
        });

        if (response.data.data.Status === 'OK') {
          toast.success(`${actionText}成功`);
          onActionComplete?.();
        } else {
          throw new Error(response.data.message || '操作失败');
        }
      }
    });
  }, [processTaskId, processTaskStepId]);

  return {
    [AllAction.START]: handleStartTask,
    [AllAction.ACCEPT]: handleStartTask,
    [AllAction.SAVE]: handleSaveTask,
    [AllAction.COMPLETE]: handleCompleteTask
  }
}