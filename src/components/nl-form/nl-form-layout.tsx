import { useImperativeHandle, useRef, type PropsWithChildren, type RefObject } from 'react';
import type React from 'react';
import { NLFormContext } from './nl-form-context'
import { Form } from 'antd-mobile';
import type { NLProcessInfo, NLProcessSubmitParams, ProcessTaskDraft, ProcessTaskFormConfig } from '#/entity/process';
import type { FormInstance } from 'antd-mobile/es/components/form';

interface NLFormLayoutProps extends PropsWithChildren {
  formConfig?: ProcessTaskFormConfig;
  processInfo: NLProcessInfo;
  onSubmit?: (params: NLProcessSubmitParams) => void;
  onSaveDraft?: (params: NLProcessSubmitParams) => void;
  draft?: ProcessTaskDraft;
  ref?: RefObject<{form: FormInstance} | null>;
  isEditable?: boolean;
}
const NLFormLayout: React.FC<NLFormLayoutProps> = (props) => {
  const { children, processInfo, onSubmit, onSaveDraft, draft, ref, isEditable } = props;
  const [form] = Form.useForm()

  useImperativeHandle(ref, () => ({
    form
  }))

  return (
    <NLFormContext.Provider value={{ form, processInfo, draft, onSubmit, onSaveDraft}}>
      <div className='relative'>
        <div className='z-0'>
          {children}
        </div>
        {!isEditable && (
          <div className='absolute top-0 left-0 right-0 bottom-0 z-10 bg-white/45'>
          </div>
        )}
      </div>
    </NLFormContext.Provider>
  )
}

export default NLFormLayout;

export function useNLForm() {
  const ref = useRef<{form: FormInstance} | null>(null);
  return ref;
}

