import type { NLFormComponentConfig, NLFormComponentMapping } from '#/entity/process/nl-form-components';
import { useNLRequest } from '@/hooks/use-nl-request';
import { useMemo } from 'react';


function useNLMatrix(config: NLFormComponentConfig, uniqueKey: string, additionalFilterList?: any[]) {
  const dataSourceType = config.dataSource
  const matrixUuid = config.matrixUuid;
  const mapping = config.mapping
  const textField = mapping?.text;
  const valueField = mapping?.value;

  // 合并配置中的过滤条件和额外的过滤条件
  const configFilterList = useMemo(() =>
    config.sourceColumnList?.map((item) => ({
      uuid: item.column,
      expression: item.expression,
      valueList: item.valueList
    })) || []
  , [config.sourceColumnList]);

  const hiddenFieldList = useMemo(() =>
    config.hiddenFieldList?.map((item) => item.uuid)
  , [config.hiddenFieldList]);

  const finalFilterList = useMemo(() =>
    [...configFilterList, ...(additionalFilterList || [])]
  , [configFilterList, additionalFilterList]);

  const { data } = useNLRequest<{ dataList?: NLFormComponentMapping[]}>(
    dataSourceType === 'matrix' ? ['/api/rest/matrix/column/data/search/forselect', {
      filterList: finalFilterList,
      hiddenFieldList,
      currentPage: 1,
      pageSize: 1000,
      matrixUuid,
      textField,
      valueField
    }, 'post', uniqueKey] : null,
    {
      revalidateOnFocus: false
    }
  )

  return data?.dataList || []
}

export default useNLMatrix;