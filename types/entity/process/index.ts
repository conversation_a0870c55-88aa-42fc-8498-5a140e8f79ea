import type { NLResult } from '#/api';
import type { UserProfiles } from '../auth';
import type { NLF<PERSON><PERSON><PERSON>ponent, Hide<PERSON><PERSON>po<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NLForm<PERSON>ayout<PERSON><PERSON><PERSON>, NLFormComponentMapping} from './nl-form-components'

export interface NLCatagory {
  name: string;
  uuid: string;
  list: NLChannel[]
}

export interface NLChannel {
  startPage: number;
  color: string;
  icon: string;
  viewAuthorityList: any[];
  isActive: number;
  type: string;
  uuid: string;
  reportAuthorityList: any[];
  childrenCount: number;
  typeAndUuid: string;
  isDisplayPriority: number;
  policyLimit: number;
  channelTypeUuid: string;
  name: string;
  isActivePriority: number;
  support: string;
  desc: string;
  isFavorite: number;
}

export interface NLChannelSearchResponse {
  favoriteList: any[];
  list: NLCatagory[]
}

export interface NLProcessInfo {
  channelPath: string;
  color: string;
  icon: string;
  description: string;
  channelUuid: string;
  priorityUuid: string;
  processTaskId?: string;
}

export interface ProcessTaskDraft {
  channelPath: string;
  channelType: ChannelType;
  channelUuid: string;
  channelVo: ChannelVo;
  commentList: any[];
  defaultPriorityUuid: string;
  formAttributeDataMap: Record<string, NLFormItemValue['dataList']>;
  formAttributeHideList: any[];
  formConfig: ProcessTaskFormConfig;
  isActivePriority: number;
  isDeleted: number;
  isDisplayPriority: number;
  isFocus: number;
  isHasOldFormProp: number;
  isShowBaseInfo: number;
  isShowProcessTaskStepCommentEditorToolbar: number;
  isShowStepList: number;
  policyLimit: number;
  priorityUuid: string;
  processDispatcherList: any[];
  processUuid: string;
  redoStepList: any[];
  source: string;
  sourceName: string;
  startPage: number;
  startProcessTaskStep: ProcessStep;
  currentProcessTaskStep: ProcessStep;
  stepList: any[];
  tranferReportProcessTaskList: any[];
  worktimeUuid: string;
  id?: string;
  title?: string;
  processTaskId?: string;
}

// 工单详情接口返回的数据结构
export interface ProcessTaskDetailResponse {
  processTask: ProcessTaskDraft;
  processTaskTabLayout?: any;
  processTaskRelationCount?: number;
}

interface Comment {
  actionType: string;
  content: string;
  fileIdList: number[];
  fileList: UploadRespone[];
  policyLimit: number;
  startPage: number;
}
export interface ProcessTaskFormConfig {
  formExtendConfig: FormExtendConfig;
  reaction: RowReaction;
  hideComponentList: HideComponentList[];
  _type: string;
  readOnly: boolean;
  lcd: number;
  uuid: string;
  defaultSceneUuid: string;
  headerList: HeaderList[];
  formCustomExtendConfig: any;
  hiddenRowList: any[];
  lefterList: LefterList[];
  name: string;
  tableList: TableList[];
  sceneList: Omit<ProcessTaskFormConfig, 'sceneList'>[];
  lcu: string;
  inherit?: boolean;
}

export interface TableList {
  col: number;
  component?: NLFormComponent;
  row: number;
}

export interface LefterList {
  height: number;
}

export interface HeaderList {
  width: number;
}

export interface RowReaction {
  displayrow: Displayrow[];
  hiderow: Hiderow[];
}

export interface Hiderow {
  conditionGroupList: ConditionGroupList[];
  rows: number[];
  conditionGroupRelList: any[];
}

export interface ConditionGroupList {
  conditionList: ConditionList[];
  conditionRelList: any[];
  uuid: string;
}

export interface ConditionList {
  formItemUuid: string;
  expression: string;
  valueList: string[];
  uuid: string;
}

export interface Displayrow {
  rows: any[];
}

export interface FormExtendConfig {
  attributeList: any[];
}


export interface ChannelVo {
  channelTypeUuid: string;
  childrenCount: number;
  color: string;
  config: {
    channelRelationList: any[];
    allowTranferReport: number;
  };
  desc: string;
  icon: string;
  isActive: number;
  isActivePriority: number;
  isDisplayPriority: number;
  name: string;
  parentUuid: string;
  policyLimit: number;
  reportAuthorityList: any[];
  startPage: number;
  support: string;
  type: string;
  typeAndUuid: string;
  uuid: string;
  viewAuthorityList: any[];
}

export interface ChannelType {
  color: string;
  description: string;
  isActive: number;
  name: string;
  policyLimit: number;
  prefix: string;
  sort: number;
  startPage: number;
  uuid: string;
}

export interface UploadParams {
  param: 'file',
  type: 'formuploadfile',
  responseType: 'blob',
  file: any
}

export interface UploadRespone {
  actionType: string;
  contentType: string;
  ext: string;
  id: string;
  name: string;
  path: string;
  pathName: string;
  policyLimit: number;
  size: number;
  sizeText: string;
  startPage: number;
  type: string;
  typeText: string;
  uploadTime: number;
  url: string;
  userUuid: string;
}

export type NLFormItemFileListValue = Array<{
    id: string;
    name: string;
    response: NLResult<UploadRespone>
  }>

export interface NLFormItemValue {
  attributeUuid: string;
  key: string;
  handler: NLFormHandler | NLFormLayoutHandler;
  dataList?: string | number | NLFormComponentMapping | Array<string | number> | Array<NLFormComponentMapping> | NLFormItemFileListValue
}

export interface NLProcessSubmitParams {
  channelUuid: string;
  title: string;
  formAttributeDataList: NLFormItemValue[];
  hidecomponentList: any[];
  readcomponentList: any[];
  formExtendAttributeDataList: any[];
  content: string;
  fileIdList: string[];
  owner: string;
  priorityUuid: string;
  focusUserUuidList: any[];
  tagList: any[];
  processTaskId?: string;
}

export interface NLProcessSaveDraftResponse {
  processTaskId: string;
  processTaskStepId: string;
}

export interface ProcessStep {
  actionList: any[];
  assignableWorkerStepList: any[];
  backwardNextStepList: any[];
  comment?: Comment;
  commentList: any[];
  formAttributeVoList: any[];
  formSceneUuid: string;
  forwardNextStepList: any[];
  handler: string;
  isActive: number;
  isAllDone: boolean;
  isCurrentUserDone: boolean;
  isNeedContent: number;
  isNeedUploadFile: number;
  isRequired: number;
  minorUserList: any[];
  name: string;
  policyLimit: number;
  processStepUuid: string;
  processTaskStepRemindList: any[];
  processTaskStepTask: Record<string, string>;
  processUuid: string;
  slaTimeList: NLSlaTime[];
  startPage: number;
  type: string;
  userList: any[];
  workerList: any[];
  workerPolicyList: any[];
  aliasName: string;
  flowDirection: string;
  id: string;
  processTaskId: number;
  status: string;
}

export interface NLProcessTaskDetail {
  action: string;
  actionTime: number;
  auditDetailList: AuditDetail[];
  description: string;
  formSceneUuid: string;
  id: number;
  processTaskId: number;
  processTaskStepId: number;
  source: string;
  sourceName: string;
  stepStatus: string;
  stepStatusVo: StepStatusVo;
  userUuid: string;
  userVo: UserProfiles;
}


interface StepStatusVo {
  color: string;
  status: string;
  text: string;
}

export interface AuditDetail {
  auditId: number;
  changeType: string;
  newContent: string;
  type: string;
  typeName: string;
  oldContent?: string;
}


export interface NLProcessRecord {
  actionList: any[];
  activeTime: number;
  assignableWorkerStepList: any[];
  backwardNextStepList: any[];
  comment?: Comment;
  commentList: NLTaskRecordComment[];
  configHash: string;
  customButtonList: CustomButton[];
  customStatusList: CustomButton[];
  enableReapproval: number;
  endTime?: number;
  formAttributeVoList: any[];
  formSceneUuid: string;
  forwardNextStepList: any[];
  handler: string;
  id: number;
  isActive: number;
  isAllDone: boolean;
  isCurrentUserDone: boolean;
  isInTheCurrentStepTab: number;
  isNeedContent: number;
  isNeedUploadFile: number;
  isRequired: number;
  isView: number;
  majorUser: MajorUser;
  minorUserList: any[];
  name: string;
  policyLimit: number;
  processStepUuid: string;
  processTaskId: number;
  processTaskStepRemindList: any[];
  processTaskStepTask: any;
  replaceableTextList: any[];
  slaTimeList: NLSlaTime[];
  startPage: number;
  startTime: number;
  status: string;
  statusVo: StatusVo;
  type: string;
  userList: any[];
  workerList: any[];
  workerPolicyList: any[];
}

interface StatusVo {
  color: string;
  status: string;
  text: string;
}

export interface NLSlaTime {
  calculationTime: number;
  calculationTimeLong: number;
  expireTime: number;
  expireTimeLong: number;
  name: string;
  processTaskId: number;
  realExpireTime: number;
  realExpireTimeLong: number;
  realTimeLeft: number;
  slaId: number;
  slaTimeDisplayMode: string;
  status: string;
  timeLeft: number;
  timeSum: number;
}

interface MajorUser {
  activeTime: number;
  endTime?: number;
  processTaskId: number;
  processTaskStepId: number;
  startTime: number;
  status: string;
  statusName: string;
  userName: string;
  userType: string;
  userUuid: string;
  userVo: UserProfiles;
}


export interface NLTaskRecordComment {
  actionType: string;
  content: string;
  fcd: number;
  fcu: string;
  fcuVo: UserProfiles;
  fileIdList: any[];
  fileList: any[];
  id: number;
  isDeletable: number;
  isEditable: number;
  lcd: number;
  lcu: string;
  lcuVo: UserProfiles;
  operatorRole: string;
  policyLimit: number;
  processTaskId: number;
  processTaskStepId: number;
  source: string;
  sourceName: string;
  startPage: number;
  targetList: any[];
  type: string;
}

interface CustomButton {
  name: string;
  text: string;
  value: string;
}